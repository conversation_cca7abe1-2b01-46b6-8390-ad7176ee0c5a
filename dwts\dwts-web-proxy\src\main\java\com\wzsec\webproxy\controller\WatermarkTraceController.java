package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.service.WatermarkTraceService;
import com.wzsec.webproxy.watermark.util.WatermarkExtractor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水印溯源API控制器
 * 提供水印提取和溯源分析的REST接口
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@RestController
@RequestMapping("/api/watermark/trace")
@Api(tags = "水印溯源管理")
public class WatermarkTraceController {

    @Autowired
    private WatermarkTraceService watermarkTraceService;
    
    @Autowired
    private WatermarkExtractor watermarkExtractor;

    /**
     * 分析可疑内容中的水印信息
     */
    @PostMapping("/analyze")
    @ApiOperation("分析可疑内容中的水印信息")
    public ResponseEntity<Map<String, Object>> analyzeSuspiciousContent(
            @ApiParam("可疑内容") @RequestBody String content,
            @ApiParam("内容类型") @RequestParam(defaultValue = "text/plain") String contentType,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("收到水印溯源分析请求 - 内容长度: {}, 类型: {}, 来源IP: {}", 
                content.length(), contentType, request.getRemoteAddr());
            
            if (content == null || content.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "内容不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 执行溯源分析
            WatermarkTraceService.TraceAnalysisResult result = 
                watermarkTraceService.analyzeSuspiciousContent(content, contentType);
            
            response.put("success", true);
            response.put("data", result);
            response.put("message", "分析完成");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("水印溯源分析失败", e);
            response.put("success", false);
            response.put("message", "分析失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 从文件中提取水印信息
     */
    @PostMapping("/extract-from-file")
    @ApiOperation("从上传文件中提取水印信息")
    public ResponseEntity<Map<String, Object>> extractFromFile(
            @ApiParam("上传的文件") @RequestParam("file") MultipartFile file,
            @ApiParam("内容类型") @RequestParam(required = false) String contentType,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (file.isEmpty()) {
                response.put("success", false);
                response.put("message", "文件不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 读取文件内容
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            
            // 如果没有指定内容类型，尝试从文件名推断
            if (contentType == null || contentType.trim().isEmpty()) {
                contentType = inferContentType(file.getOriginalFilename());
            }
            
            log.info("从文件提取水印 - 文件名: {}, 大小: {}, 类型: {}, 来源IP: {}", 
                file.getOriginalFilename(), file.getSize(), contentType, request.getRemoteAddr());
            
            // 提取水印
            List<WatermarkExtractor.ExtractedWatermark> watermarks = 
                watermarkExtractor.extractWatermarks(content, contentType);
            
            // 生成报告
            WatermarkExtractor.TraceReport report = watermarkExtractor.generateTraceReport(watermarks);
            
            Map<String, Object> result = new HashMap<>();
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            result.put("contentType", contentType);
            result.put("watermarkCount", watermarks.size());
            result.put("watermarks", watermarks);
            result.put("traceReport", report);
            
            response.put("success", true);
            response.put("data", result);
            response.put("message", String.format("成功提取 %d 个水印", watermarks.size()));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("从文件提取水印失败", e);
            response.put("success", false);
            response.put("message", "提取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 快速检测内容是否包含水印
     */
    @PostMapping("/quick-detect")
    @ApiOperation("快速检测内容是否包含水印")
    public ResponseEntity<Map<String, Object>> quickDetect(
            @ApiParam("待检测内容") @RequestBody String content,
            @ApiParam("内容类型") @RequestParam(defaultValue = "text/plain") String contentType) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (content == null || content.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "内容不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 快速检测
            List<WatermarkExtractor.ExtractedWatermark> watermarks = 
                watermarkExtractor.extractWatermarks(content, contentType);
            
            boolean hasWatermark = !watermarks.isEmpty();
            
            Map<String, Object> result = new HashMap<>();
            result.put("hasWatermark", hasWatermark);
            result.put("watermarkCount", watermarks.size());
            result.put("contentLength", content.length());
            result.put("contentType", contentType);
            
            if (hasWatermark) {
                // 提供基本统计信息
                Map<String, Integer> userStats = new HashMap<>();
                Map<String, Integer> ipStats = new HashMap<>();
                
                for (WatermarkExtractor.ExtractedWatermark watermark : watermarks) {
                    String userId = watermark.getWatermarkInfo().getUserId();
                    String ip = watermark.getWatermarkInfo().getIpAddress();
                    
                    userStats.merge(userId, 1, Integer::sum);
                    ipStats.merge(ip, 1, Integer::sum);
                }
                
                result.put("uniqueUsers", userStats.size());
                result.put("uniqueIPs", ipStats.size());
                result.put("userStats", userStats);
                result.put("ipStats", ipStats);
            }
            
            response.put("success", true);
            response.put("data", result);
            response.put("message", hasWatermark ? "检测到水印" : "未检测到水印");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("快速检测失败", e);
            response.put("success", false);
            response.put("message", "检测失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取水印统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取水印统计信息")
    public ResponseEntity<Map<String, Object>> getStatistics(
            @ApiParam("开始时间戳") @RequestParam(required = false) Long startTime,
            @ApiParam("结束时间戳") @RequestParam(required = false) Long endTime) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这里可以实现基于时间范围的统计查询
            // 由于当前没有持久化水印记录，这里返回模拟数据
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalAnalysisCount", 0);
            statistics.put("watermarkDetectedCount", 0);
            statistics.put("riskLevelDistribution", new HashMap<String, Integer>());
            statistics.put("topUsers", new HashMap<String, Integer>());
            statistics.put("topIPs", new HashMap<String, Integer>());
            
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "统计信息获取成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 验证水印完整性
     */
    @PostMapping("/verify")
    @ApiOperation("验证水印完整性")
    public ResponseEntity<Map<String, Object>> verifyWatermark(
            @ApiParam("原始内容") @RequestParam String originalContent,
            @ApiParam("待验证内容") @RequestParam String contentToVerify,
            @ApiParam("内容类型") @RequestParam(defaultValue = "text/plain") String contentType) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 提取两个内容的水印
            List<WatermarkExtractor.ExtractedWatermark> originalWatermarks = 
                watermarkExtractor.extractWatermarks(originalContent, contentType);
            
            List<WatermarkExtractor.ExtractedWatermark> verifyWatermarks = 
                watermarkExtractor.extractWatermarks(contentToVerify, contentType);
            
            // 比较水印
            boolean isIntact = compareWatermarks(originalWatermarks, verifyWatermarks);
            
            Map<String, Object> result = new HashMap<>();
            result.put("isIntact", isIntact);
            result.put("originalWatermarkCount", originalWatermarks.size());
            result.put("verifyWatermarkCount", verifyWatermarks.size());
            result.put("integrityScore", calculateIntegrityScore(originalWatermarks, verifyWatermarks));
            
            response.put("success", true);
            response.put("data", result);
            response.put("message", isIntact ? "水印完整" : "水印可能被篡改");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("水印验证失败", e);
            response.put("success", false);
            response.put("message", "验证失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 推断内容类型
     */
    private String inferContentType(String fileName) {
        if (fileName == null) {
            return "text/plain";
        }
        
        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".json")) {
            return "application/json";
        } else if (lowerFileName.endsWith(".xml")) {
            return "application/xml";
        } else if (lowerFileName.endsWith(".html") || lowerFileName.endsWith(".htm")) {
            return "text/html";
        } else {
            return "text/plain";
        }
    }

    /**
     * 比较水印
     */
    private boolean compareWatermarks(List<WatermarkExtractor.ExtractedWatermark> original,
                                    List<WatermarkExtractor.ExtractedWatermark> verify) {
        if (original.size() != verify.size()) {
            return false;
        }
        
        // 简单比较，实际应用中可能需要更复杂的逻辑
        for (int i = 0; i < original.size(); i++) {
            WatermarkExtractor.WatermarkInfo originalInfo = original.get(i).getWatermarkInfo();
            WatermarkExtractor.WatermarkInfo verifyInfo = verify.get(i).getWatermarkInfo();
            
            if (!originalInfo.getUserId().equals(verifyInfo.getUserId()) ||
                !originalInfo.getIpAddress().equals(verifyInfo.getIpAddress()) ||
                originalInfo.getTimestamp() != verifyInfo.getTimestamp()) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 计算完整性分数
     */
    private double calculateIntegrityScore(List<WatermarkExtractor.ExtractedWatermark> original,
                                         List<WatermarkExtractor.ExtractedWatermark> verify) {
        if (original.isEmpty() && verify.isEmpty()) {
            return 1.0;
        }
        
        if (original.isEmpty() || verify.isEmpty()) {
            return 0.0;
        }
        
        int matchCount = 0;
        int totalCount = Math.max(original.size(), verify.size());
        
        for (WatermarkExtractor.ExtractedWatermark originalWatermark : original) {
            for (WatermarkExtractor.ExtractedWatermark verifyWatermark : verify) {
                if (watermarksMatch(originalWatermark, verifyWatermark)) {
                    matchCount++;
                    break;
                }
            }
        }
        
        return (double) matchCount / totalCount;
    }

    /**
     * 判断两个水印是否匹配
     */
    private boolean watermarksMatch(WatermarkExtractor.ExtractedWatermark w1, 
                                  WatermarkExtractor.ExtractedWatermark w2) {
        WatermarkExtractor.WatermarkInfo info1 = w1.getWatermarkInfo();
        WatermarkExtractor.WatermarkInfo info2 = w2.getWatermarkInfo();
        
        return info1.getUserId().equals(info2.getUserId()) &&
               info1.getIpAddress().equals(info2.getIpAddress()) &&
               info1.getTimestamp() == info2.getTimestamp() &&
               info1.getSessionId().equals(info2.getSessionId());
    }
}
