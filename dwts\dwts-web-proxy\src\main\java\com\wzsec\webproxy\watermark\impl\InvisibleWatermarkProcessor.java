package com.wzsec.webproxy.watermark.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.AbstractWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * 不可见暗水印处理器
 * 使用零宽字符技术在API响应中嵌入不可见水印，支持溯源追踪
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class InvisibleWatermarkProcessor extends AbstractWatermarkProcessor {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 零宽字符定义
    private static final char ZERO_WIDTH_SPACE = '\u200B';           // 零宽空格
    private static final char ZERO_WIDTH_NON_JOINER = '\u200C';      // 零宽非连接符
    private static final char ZERO_WIDTH_JOINER = '\u200D';          // 零宽连接符
    private static final char WORD_JOINER = '\u2060';                // 词连接符
    private static final char INVISIBLE_SEPARATOR = '\u2062';        // 不可见分隔符
    
    // 编码映射表
    private static final Map<String, Character> ENCODING_MAP = new HashMap<>();
    static {
        ENCODING_MAP.put("00", ZERO_WIDTH_SPACE);
        ENCODING_MAP.put("01", ZERO_WIDTH_NON_JOINER);
        ENCODING_MAP.put("10", ZERO_WIDTH_JOINER);
        ENCODING_MAP.put("11", WORD_JOINER);
    }
    
    // 解码映射表
    private static final Map<Character, String> DECODING_MAP = new HashMap<>();
    static {
        DECODING_MAP.put(ZERO_WIDTH_SPACE, "00");
        DECODING_MAP.put(ZERO_WIDTH_NON_JOINER, "01");
        DECODING_MAP.put(ZERO_WIDTH_JOINER, "10");
        DECODING_MAP.put(WORD_JOINER, "11");
    }

    @Override
    public byte[] processWatermark(byte[] content, String contentType, 
                                 HttpServletRequest request, WebProxyConfig config) {
        try {
            if (!config.getEnableApiWatermark()) {
                return content;
            }

            String contentString = safeToString(content);
            if (contentString.trim().isEmpty()) {
                return content;
            }

            // 生成水印信息
            WatermarkInfo watermarkInfo = generateWatermarkInfo(request, config);
            
            // 根据内容类型处理
            String watermarkedContent;
            if (isJsonContent(contentType)) {
                watermarkedContent = processJsonContent(contentString, watermarkInfo);
            } else if (isXmlContent(contentType)) {
                watermarkedContent = processXmlContent(contentString, watermarkInfo);
            } else {
                watermarkedContent = processTextContent(contentString, watermarkInfo);
            }
            
            log.debug("暗水印处理完成 - 原始长度: {}, 处理后长度: {}", 
                     contentString.length(), watermarkedContent.length());
            
            return safeToBytes(watermarkedContent);
            
        } catch (Exception e) {
            log.error("暗水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    /**
     * 处理JSON内容
     */
    private String processJsonContent(String jsonString, WatermarkInfo watermarkInfo) throws Exception {
        JsonNode rootNode = objectMapper.readTree(jsonString);
        JsonNode watermarkedNode = injectInvisibleWatermarkToJson(rootNode, watermarkInfo);
        return objectMapper.writeValueAsString(watermarkedNode);
    }

    /**
     * 向JSON中注入不可见水印
     */
    private JsonNode injectInvisibleWatermarkToJson(JsonNode node, WatermarkInfo watermarkInfo) {
        if (node.isObject()) {
            return processObjectNode((ObjectNode) node, watermarkInfo);
        } else if (node.isArray()) {
            return processArrayNode((ArrayNode) node, watermarkInfo);
        } else if (node.isTextual()) {
            return processTextNode((TextNode) node, watermarkInfo);
        }
        return node;
    }

    /**
     * 处理JSON对象节点
     */
    private ObjectNode processObjectNode(ObjectNode objectNode, WatermarkInfo watermarkInfo) {
        ObjectNode result = objectNode.deepCopy();
        
        // 在字符串字段中嵌入暗水印
        result.fields().forEachRemaining(entry -> {
            JsonNode value = entry.getValue();
            if (value.isTextual() && shouldEmbedWatermark(entry.getKey(), value.asText())) {
                String watermarkedText = embedInvisibleWatermark(value.asText(), watermarkInfo);
                result.put(entry.getKey(), watermarkedText);
            } else if (value.isObject() || value.isArray()) {
                result.set(entry.getKey(), injectInvisibleWatermarkToJson(value, watermarkInfo));
            }
        });
        
        return result;
    }

    /**
     * 处理JSON数组节点
     */
    private ArrayNode processArrayNode(ArrayNode arrayNode, WatermarkInfo watermarkInfo) {
        ArrayNode result = arrayNode.deepCopy();
        
        for (int i = 0; i < result.size(); i++) {
            JsonNode element = result.get(i);
            if (element.isTextual()) {
                String watermarkedText = embedInvisibleWatermark(element.asText(), watermarkInfo);
                result.set(i, watermarkedText);
            } else if (element.isObject() || element.isArray()) {
                result.set(i, injectInvisibleWatermarkToJson(element, watermarkInfo));
            }
        }
        
        return result;
    }

    /**
     * 处理文本节点
     */
    private TextNode processTextNode(TextNode textNode, WatermarkInfo watermarkInfo) {
        String originalText = textNode.asText();
        String watermarkedText = embedInvisibleWatermark(originalText, watermarkInfo);
        return new TextNode(watermarkedText);
    }

    /**
     * 处理XML内容
     */
    private String processXmlContent(String xmlString, WatermarkInfo watermarkInfo) {
        // 在XML文本节点中嵌入暗水印
        return xmlString.replaceAll(">([^<]+)<", (matchResult) -> {
            String textContent = matchResult.group(1);
            if (textContent.trim().length() > 0 && !textContent.trim().matches("^[\\d\\s.,]+$")) {
                String watermarkedText = embedInvisibleWatermark(textContent, watermarkInfo);
                return ">" + watermarkedText + "<";
            }
            return matchResult.group(0);
        });
    }

    /**
     * 处理纯文本内容
     */
    private String processTextContent(String textContent, WatermarkInfo watermarkInfo) {
        return embedInvisibleWatermark(textContent, watermarkInfo);
    }

    /**
     * 在文本中嵌入不可见水印
     */
    private String embedInvisibleWatermark(String originalText, WatermarkInfo watermarkInfo) {
        if (originalText == null || originalText.trim().isEmpty()) {
            return originalText;
        }

        try {
            // 生成水印编码
            String watermarkCode = encodeWatermarkInfo(watermarkInfo);
            
            // 将水印编码转换为零宽字符
            String invisibleWatermark = encodeToZeroWidthChars(watermarkCode);
            
            // 智能嵌入位置选择
            List<Integer> embedPositions = selectEmbedPositions(originalText, invisibleWatermark.length());
            
            StringBuilder result = new StringBuilder(originalText);
            int offset = 0;
            
            for (int i = 0; i < invisibleWatermark.length() && i < embedPositions.size(); i++) {
                int position = embedPositions.get(i) + offset;
                char watermarkChar = invisibleWatermark.charAt(i);
                result.insert(position, watermarkChar);
                offset++;
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.warn("嵌入暗水印失败: {}", e.getMessage());
            return originalText;
        }
    }

    /**
     * 编码水印信息
     */
    private String encodeWatermarkInfo(WatermarkInfo watermarkInfo) throws Exception {
        // 创建紧凑的水印数据
        String watermarkData = String.format("%s|%s|%d|%s", 
            watermarkInfo.getUserId(),
            watermarkInfo.getIpAddress(),
            watermarkInfo.getTimestamp(),
            watermarkInfo.getSessionId().substring(0, Math.min(8, watermarkInfo.getSessionId().length()))
        );
        
        // 计算校验和
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hash = md.digest(watermarkData.getBytes(StandardCharsets.UTF_8));
        String checksum = Base64.getEncoder().encodeToString(hash).substring(0, 4);
        
        return watermarkData + "|" + checksum;
    }

    /**
     * 将字符串编码为零宽字符
     */
    private String encodeToZeroWidthChars(String data) {
        StringBuilder result = new StringBuilder();
        
        // 添加起始标记
        result.append(INVISIBLE_SEPARATOR);
        
        for (char c : data.toCharArray()) {
            String binary = String.format("%8s", Integer.toBinaryString(c)).replace(' ', '0');
            
            // 每2位二进制转换为一个零宽字符
            for (int i = 0; i < binary.length(); i += 2) {
                String bits = binary.substring(i, Math.min(i + 2, binary.length()));
                if (bits.length() == 1) bits += "0"; // 补齐
                
                Character zeroWidthChar = ENCODING_MAP.get(bits);
                if (zeroWidthChar != null) {
                    result.append(zeroWidthChar);
                }
            }
        }
        
        // 添加结束标记
        result.append(INVISIBLE_SEPARATOR);
        
        return result.toString();
    }

    /**
     * 选择嵌入位置
     */
    private List<Integer> selectEmbedPositions(String text, int watermarkLength) {
        List<Integer> positions = new ArrayList<>();
        Random random = new Random();
        
        // 在单词边界和标点符号后选择位置
        for (int i = 0; i < text.length() && positions.size() < watermarkLength; i++) {
            char c = text.charAt(i);
            if (Character.isWhitespace(c) || isPunctuation(c)) {
                if (i + 1 < text.length()) {
                    positions.add(i + 1);
                }
            }
        }
        
        // 如果位置不够，随机选择
        while (positions.size() < watermarkLength && positions.size() < text.length()) {
            int pos = random.nextInt(text.length());
            if (!positions.contains(pos)) {
                positions.add(pos);
            }
        }
        
        Collections.sort(positions);
        return positions;
    }

    /**
     * 判断是否应该嵌入水印
     */
    private boolean shouldEmbedWatermark(String fieldName, String value) {
        // 跳过敏感字段和短文本
        if (fieldName.toLowerCase().contains("password") || 
            fieldName.toLowerCase().contains("token") ||
            fieldName.toLowerCase().contains("key") ||
            value.length() < 10) {
            return false;
        }
        
        // 只在文本内容中嵌入
        return value.matches(".*[a-zA-Z\u4e00-\u9fa5].*");
    }

    /**
     * 判断是否为标点符号
     */
    private boolean isPunctuation(char c) {
        return ".,;:!?()[]{}\"'".indexOf(c) >= 0;
    }

    /**
     * 判断是否为JSON内容
     */
    private boolean isJsonContent(String contentType) {
        return contentType != null && 
               (contentType.toLowerCase().contains("application/json") ||
                contentType.toLowerCase().contains("text/json"));
    }

    /**
     * 判断是否为XML内容
     */
    private boolean isXmlContent(String contentType) {
        return contentType != null && 
               (contentType.toLowerCase().contains("application/xml") ||
                contentType.toLowerCase().contains("text/xml"));
    }

    /**
     * 生成水印信息
     */
    private WatermarkInfo generateWatermarkInfo(HttpServletRequest request, WebProxyConfig config) {
        return WatermarkInfo.builder()
            .userId(getCurrentUser(request))
            .ipAddress(getClientIpAddress(request))
            .timestamp(System.currentTimeMillis())
            .sessionId(request.getSession().getId())
            .proxyName(config.getProxyName())
            .requestPath(request.getRequestURI())
            .build();
    }

    @Override
    public boolean canHandle(String contentType) {
        return true; // 可以处理所有类型的内容
    }

    @Override
    public String getProcessorName() {
        return "InvisibleWatermarkProcessor";
    }

    @Override
    public String getWatermarkType() {
        return "INVISIBLE";
    }

    /**
     * 水印信息数据类
     */
    public static class WatermarkInfo {
        private String userId;
        private String ipAddress;
        private long timestamp;
        private String sessionId;
        private String proxyName;
        private String requestPath;

        public static WatermarkInfoBuilder builder() {
            return new WatermarkInfoBuilder();
        }

        // Getters
        public String getUserId() { return userId; }
        public String getIpAddress() { return ipAddress; }
        public long getTimestamp() { return timestamp; }
        public String getSessionId() { return sessionId; }
        public String getProxyName() { return proxyName; }
        public String getRequestPath() { return requestPath; }

        public static class WatermarkInfoBuilder {
            private WatermarkInfo info = new WatermarkInfo();
            
            public WatermarkInfoBuilder userId(String userId) {
                info.userId = userId;
                return this;
            }
            
            public WatermarkInfoBuilder ipAddress(String ipAddress) {
                info.ipAddress = ipAddress;
                return this;
            }
            
            public WatermarkInfoBuilder timestamp(long timestamp) {
                info.timestamp = timestamp;
                return this;
            }
            
            public WatermarkInfoBuilder sessionId(String sessionId) {
                info.sessionId = sessionId;
                return this;
            }
            
            public WatermarkInfoBuilder proxyName(String proxyName) {
                info.proxyName = proxyName;
                return this;
            }
            
            public WatermarkInfoBuilder requestPath(String requestPath) {
                info.requestPath = requestPath;
                return this;
            }
            
            public WatermarkInfo build() {
                return info;
            }
        }
    }
}
