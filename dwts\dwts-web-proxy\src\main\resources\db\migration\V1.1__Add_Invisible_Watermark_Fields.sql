-- 添加暗水印相关字段到 dwts_web_proxy_config 表
ALTER TABLE dwts_web_proxy_config
ADD COLUMN enable_invisible_watermark BOOLEAN DEFAULT TRUE COMMENT '是否启用暗水印';

ALTER TABLE dwts_web_proxy_config
ADD COLUMN invisible_encoding_strength VARCHAR(20) DEFAULT 'medium' COMMENT '暗水印编码强度(low/medium/high)';

ALTER TABLE dwts_web_proxy_config
ADD COLUMN invisible_embed_density DECIMAL(3,2) DEFAULT 0.30 COMMENT '暗水印嵌入密度(0.1-1.0)';

-- 创建水印溯源记录表
CREATE TABLE IF NOT EXISTS watermark_trace_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    trace_id VARCHAR(64) NOT NULL COMMENT '溯源ID',
    content_hash VARCHAR(64) NOT NULL COMMENT '内容哈希',
    content_type VARCHAR(100) COMMENT '内容类型',
    content_length INT COMMENT '内容长度',
    watermark_count INT DEFAULT 0 COMMENT '检测到的水印数量',
    risk_level VARCHAR(20) COMMENT '风险等级',
    confidence_score DECIMAL(3,2) COMMENT '可信度分数',
    analysis_result TEXT COMMENT '分析结果JSON',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    user_agent TEXT COMMENT '用户代理',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_trace_id (trace_id),
    INDEX idx_content_hash (content_hash),
    INDEX idx_client_ip (client_ip),
    INDEX idx_create_time (create_time),
    INDEX idx_risk_level (risk_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水印溯源记录表';

-- 创建水印提取详情表
CREATE TABLE IF NOT EXISTS watermark_extract_detail (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    trace_record_id BIGINT NOT NULL COMMENT '溯源记录ID',
    location VARCHAR(500) COMMENT '水印位置',
    user_id VARCHAR(100) COMMENT '用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    session_id VARCHAR(100) COMMENT '会话ID',
    timestamp BIGINT COMMENT '时间戳',
    proxy_name VARCHAR(100) COMMENT '代理名称',
    request_path VARCHAR(500) COMMENT '请求路径',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_trace_record_id (trace_record_id),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_session_id (session_id),
    INDEX idx_timestamp (timestamp),
    
    FOREIGN KEY (trace_record_id) REFERENCES watermark_trace_record(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水印提取详情表';

-- 更新现有配置，启用暗水印功能
UPDATE dwts_web_proxy_config
SET enable_invisible_watermark = TRUE,
    invisible_encoding_strength = 'medium',
    invisible_embed_density = 0.30
WHERE enable_api_watermark = TRUE;
