package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WatermarkConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水印配置管理API控制器
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@RestController
@RequestMapping("/api/watermark/config")
@Api(tags = "水印配置管理")
public class WatermarkConfigController {

    @Autowired
    private WatermarkConfigService watermarkConfigService;

    /**
     * 获取所有水印配置
     */
    @GetMapping("/list")
    @ApiOperation("获取所有水印配置")
    public ResponseEntity<Map<String, Object>> getAllConfigs() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<WebProxyConfig> configs = watermarkConfigService.getAllWatermarkConfigs();
            
            response.put("success", true);
            response.put("data", configs);
            response.put("total", configs.size());
            response.put("message", "获取配置列表成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取配置列表失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取指定代理的水印配置
     */
    @GetMapping("/proxy/{proxyName}")
    @ApiOperation("获取指定代理的水印配置")
    public ResponseEntity<Map<String, Object>> getConfigByProxyName(
            @ApiParam("代理名称") @PathVariable String proxyName) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            WebProxyConfig config = watermarkConfigService.getWatermarkConfigByProxyName(proxyName);
            
            if (config != null) {
                response.put("success", true);
                response.put("data", config);
                response.put("message", "获取配置成功");
            } else {
                response.put("success", false);
                response.put("message", "未找到指定代理的配置");
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取代理配置失败: {}", proxyName, e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新暗水印配置
     */
    @PutMapping("/invisible/{configId}")
    @ApiOperation("更新暗水印配置")
    public ResponseEntity<Map<String, Object>> updateInvisibleConfig(
            @ApiParam("配置ID") @PathVariable Long configId,
            @RequestBody Map<String, Object> configData) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean enableInvisible = (Boolean) configData.getOrDefault("enableInvisible", true);
            String encodingStrength = (String) configData.getOrDefault("encodingStrength", "medium");
            Double embedDensity = ((Number) configData.getOrDefault("embedDensity", 0.3)).doubleValue();
            
            // 验证配置
            Map<String, Object> validation = watermarkConfigService
                .validateInvisibleWatermarkConfig(encodingStrength, embedDensity);
            
            if (!(Boolean) validation.get("isValid")) {
                response.put("success", false);
                response.put("message", "配置验证失败: " + validation.get("errors"));
                return ResponseEntity.badRequest().body(response);
            }
            
            WebProxyConfig updatedConfig = watermarkConfigService
                .updateInvisibleWatermarkConfig(configId, enableInvisible, encodingStrength, embedDensity);
            
            response.put("success", true);
            response.put("data", updatedConfig);
            response.put("validation", validation);
            response.put("message", "配置更新成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("更新暗水印配置失败: {}", configId, e);
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 批量启用/禁用暗水印
     */
    @PutMapping("/invisible/batch")
    @ApiOperation("批量启用/禁用暗水印")
    public ResponseEntity<Map<String, Object>> batchUpdateInvisible(
            @RequestBody Map<String, Object> requestData) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<Long> configIds = (List<Long>) requestData.get("configIds");
            boolean enabled = (Boolean) requestData.getOrDefault("enabled", true);
            
            if (configIds == null || configIds.isEmpty()) {
                response.put("success", false);
                response.put("message", "配置ID列表不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            int updatedCount = watermarkConfigService.batchUpdateInvisibleWatermark(configIds, enabled);
            
            response.put("success", true);
            response.put("updatedCount", updatedCount);
            response.put("totalCount", configIds.size());
            response.put("message", String.format("批量更新完成，成功更新 %d/%d 个配置", 
                updatedCount, configIds.size()));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("批量更新暗水印配置失败", e);
            response.put("success", false);
            response.put("message", "批量更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取水印配置统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取水印配置统计信息")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> stats = watermarkConfigService.getWatermarkStatistics();
            
            response.put("success", true);
            response.put("data", stats);
            response.put("message", "获取统计信息成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 重置暗水印配置为默认值
     */
    @PostMapping("/invisible/{configId}/reset")
    @ApiOperation("重置暗水印配置为默认值")
    public ResponseEntity<Map<String, Object>> resetInvisibleConfig(
            @ApiParam("配置ID") @PathVariable Long configId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            WebProxyConfig resetConfig = watermarkConfigService.resetInvisibleWatermarkConfig(configId);
            
            response.put("success", true);
            response.put("data", resetConfig);
            response.put("message", "配置重置成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("重置配置失败: {}", configId, e);
            response.put("success", false);
            response.put("message", "重置失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 复制水印配置
     */
    @PostMapping("/copy")
    @ApiOperation("复制水印配置")
    public ResponseEntity<Map<String, Object>> copyConfig(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long sourceConfigId = ((Number) requestData.get("sourceConfigId")).longValue();
            @SuppressWarnings("unchecked")
            List<Long> targetConfigIds = (List<Long>) requestData.get("targetConfigIds");
            
            if (targetConfigIds == null || targetConfigIds.isEmpty()) {
                response.put("success", false);
                response.put("message", "目标配置ID列表不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            watermarkConfigService.copyWatermarkConfig(sourceConfigId, targetConfigIds);
            
            response.put("success", true);
            response.put("copiedCount", targetConfigIds.size());
            response.put("message", "配置复制成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("复制配置失败", e);
            response.put("success", false);
            response.put("message", "复制失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取推荐配置
     */
    @GetMapping("/recommended")
    @ApiOperation("获取推荐的水印配置")
    public ResponseEntity<Map<String, Object>> getRecommendedConfig(
            @ApiParam("安全级别") @RequestParam(defaultValue = "medium") String securityLevel) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> recommendedConfig = watermarkConfigService.getRecommendedConfig(securityLevel);
            
            response.put("success", true);
            response.put("data", recommendedConfig);
            response.put("securityLevel", securityLevel);
            response.put("message", "获取推荐配置成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取推荐配置失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 验证暗水印配置
     */
    @PostMapping("/invisible/validate")
    @ApiOperation("验证暗水印配置")
    public ResponseEntity<Map<String, Object>> validateConfig(@RequestBody Map<String, Object> configData) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String encodingStrength = (String) configData.get("encodingStrength");
            Double embedDensity = configData.get("embedDensity") != null ? 
                ((Number) configData.get("embedDensity")).doubleValue() : null;
            
            Map<String, Object> validation = watermarkConfigService
                .validateInvisibleWatermarkConfig(encodingStrength, embedDensity);
            
            response.put("success", true);
            response.put("data", validation);
            response.put("message", "配置验证完成");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("验证配置失败", e);
            response.put("success", false);
            response.put("message", "验证失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
